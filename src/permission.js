import router from './router'
import Cookies from 'js-cookie'
import { whiteRouteList } from "@/utils/whiteRouteList.js"

// 常量定义
const allowList = ['login', 'register', 'registerResult', 'agreement'] // 免登录路由名称白名单
const LOGIN_PATHS = ['/login', '/Login'] // 登录页面路径
const DEVICE_PATHS = ['/device', '/device/deviceDetail'] // 设备系统路径
const CAR_PATHS = ['/car', '/carSystem/equipmentDetail'] // 充电桩系统路径

// 获取设备首页路径
const getDeviceHome = () => {
  const businessType = localStorage.getItem('businessType')
  return businessType === 'energy_storage_cabinet' ? '/device' : '/car'
}

// 判断是否为登录页面
const isLoginPage = (path) => LOGIN_PATHS.includes(path)

// 设置活跃系统标识
const setActiveSystem = (path) => {
  if (DEVICE_PATHS.includes(path)) {
    localStorage.setItem('activeSystem', 'device')
  } else if (CAR_PATHS.includes(path)) {
    localStorage.setItem('activeSystem', 'car')
  }
}
router.beforeEach((to, from, next) => {
  const token = Cookies.get('token')
  const deviceHome = getDeviceHome()

  // 处理白名单路由
  if (whiteRouteList.includes(to.path)) {
    // 如果有token且访问登录页面，跳转到设备首页
    if (token && isLoginPage(to.path)) {
      next({ path: deviceHome })
    } else {
      // 其他白名单路由正常通过
      next()
    }
    return
  }

  // 处理非白名单路由
  if (token) {
    // 设置活跃系统标识
    setActiveSystem(to.path)

    // 如果访问根路径或登录页面，跳转到设备首页
    if (to.path === '/' || isLoginPage(to.path)) {
      next({ path: deviceHome })
    } else {
      // 其他情况正常通过
      next()
    }
  } else {
    // 无token时的处理
    if (allowList.includes(to.name)) {
      // 在免登录名单，直接进入
      next()
    } else {
      // 跳转到登录页面
      next({ path: '/Login', query: { redirect: to.fullPath } })
    }
  }
})