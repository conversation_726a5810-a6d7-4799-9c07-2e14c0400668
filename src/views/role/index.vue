<template>
    <div class="role-box">
        <div
            class="text-base leading-8 go-box flex items-center text-primary-text dark:text-80-dark"
        >
            <span
                class="cursor-pointer mr-1 a text-primary-text dark:text-80-dark"
                @click="goBlack"
                >{{ $t('Home') }}</span
            >

            <iconSvg
                name="rightIcon "
                class="more-icon text-primary-text dark:text-80-dark"
            />
            <!-- 站点详情 -->
            <span class="ml-1 text-primary-text dark:text-80-dark">{{
                $t('Settings')
            }}</span>
        </div>
        <div class="role-content">
            <a-tabs
                v-model:activeKey="activeKey"
                class="my-tab"
                @change="tabchange"
            >
                <template #tabBarExtraContent>
                    <!-- -->
                    <div v-if="activeKey == '0' && isOrgOwner">
                        <div class="h-10">
                            <div
                                class="flex items-center gap-x-2"
                                v-if="isEdit"
                            >
                                <el-button
                                    plain
                                    round
                                    class="btn-hover"
                                    @click="editClick"
                                >
                                    <span>{{ $t('common_bianji') }}</span>
                                    <span class="icon-box ml-0.5">
                                        <iconSvg
                                            name="edit"
                                            class="icon-default"
                                        />
                                    </span>
                                </el-button>
                            </div>
                            <div
                                v-if="!isEdit"
                                class="flex items-center gap-x-2"
                            >
                                <el-button
                                    plain
                                    round
                                    class="btn-hover"
                                    @click="colseClick"
                                >
                                    <span>{{ $t('Cancle') }}</span>
                                </el-button>
                                <el-button
                                    plain
                                    round
                                    type="primary"
                                    class="btn-hover"
                                    @click="submtParams"
                                >
                                    <span>{{ $t('Save') }}</span>
                                </el-button>
                            </div>
                        </div>
                    </div>
                    <div v-if="activeKey == '1'">
                        <div class="h-10 flex">
                            <el-button
                                plain
                                round
                                class="btn-hover"
                                @click="addUserFun(false)"
                                v-if="isOrgOwner"
                            >
                                <span>{{ $t('Add User') }}</span>
                                <span class="icon-box ml-0.5">
                                    <iconSvg
                                        name="addUser"
                                        className="icon-default"
                                    />
                                </span>
                            </el-button>
                        </div>
                    </div>
                    <div v-if="activeKey == '2'">
                        <div class="h-10 w-1"></div>
                        <!-- <el-button
                            plain
                            round
                            class="btn-hover"
                            @click="addChild"
                        >
                            <span>{{ $t('Add Sub-account') }}</span>
                            <span class="icon-box ml-0.5">
                                <iconSvg
                                    name="addChild"
                                    className="icon-default"
                                />
                            </span>
                        </el-button> -->
                    </div>
                    <div v-if="activeKey == '4'"></div>
                </template>
                <a-tab-pane key="0" :tab="$t('enterprise_qiyeguanli')">
                    <enterpriseDetailTop ref="enterpriseDetai" />
                </a-tab-pane>
                <a-tab-pane key="1" :tab="$t('user_yonghuguanli')">
                    <enterpriseManagement ref="enterpriseDocument" />
                </a-tab-pane>
                <a-tab-pane
                    key="2"
                    :tab="$t('sub_zichanghuguanli')"
                    v-if="isSupplier"
                >
                    <subAccount ref="subAccountDocument" />
                </a-tab-pane>
                <!-- <a-tab-pane key="3" tab="数据统计">
                    <statistics-table
                        ref="statisticsTableRef"
                        v-if="activeKey == '3' && activeSystem == 'device'"
                    />
                    <statistics-car-table
                        v-if="activeKey == '3' && activeSystem == 'car'"
                    />
                </a-tab-pane> -->
                <a-tab-pane
                    key="4"
                    :tab="$t('opeartion_caozuorizhi')"
                    v-if="isSupplier && activeSystem == 'device'"
                >
                    <operation-log
                        ref="operationLogRef"
                        v-if="activeKey == '4'"
                    />
                </a-tab-pane>
            </a-tabs>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import enterpriseManagement from './components/enterpriseManagement.vue'
import subAccount from './components/subAccount.vue'
import enterpriseDetailTop from './components/enterpriseDetailTop.vue'
import statisticsTable from './components/statisticsTable'
import StatisticsCarTable from './components/statisticsCarTable'

import OperationLog from './components/OperationLog'
import { useStore } from 'vuex'
import { message } from 'ant-design-vue'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
import moment from 'moment'

const store = useStore()
const activeKey = ref('0')
const subAccountDocument = ref(null)
const enterpriseDocument = ref(null)
const enterpriseDetai = ref(null)
const route = useRoute()

const isEdit = ref(true)

const router = useRouter()

const activeSystem = computed(() => {
    return localStorage.getItem('activeSystem')
})
const isOrgOwner = computed(() => {
    return store.state.user.userInfoData.roles.includes('org_owner')
})

const isSupplier = computed(() => {
    return store.state.user.userInfoData.orgType == 'supplier'
})

const addUserFun = () => {
    enterpriseDocument.value.addUserFun(false)
}

const addChild = () => {
    subAccountDocument.value.visible = true
}

const editClick = () => {
    isEdit.value = false
    enterpriseDetai.value.showClick(true)
}

const colseClick = () => {
    isEdit.value = true
    enterpriseDetai.value.showClick(false)
}

const submtParams = async () => {
    const {
        data: { code },
    } = await enterpriseDetai.value.modifyOrgInfo()
    if (code === 0) {
        message.success(t('Successed'))
        // Vue中调用该函数会刷新页面
        setTimeout(() => {
            window.location.reload()
        }, 300)
    }
}

const goBlack = () => {
    if (activeSystem.value == 'car') {
        router.push({
            path: '/car',
        })
    } else {
        router.push({
            path: '/device',
        })
    }
}

const operationLogRef = ref(null)
const dates = ref([])

const statisticsTableRef = ref(null)
const tabchange = (e) => {
    console.log('[ e ] >', e)
    if (e === '0') {
        console.log('[ 切换到企业管理时 ] >')
        enterpriseDetai.value.getOrgData()
    }
    colseClick()
}
onMounted(() => {
    window.scrollTo(0, 0)
    activeKey.value = route.query.activeKey || '0'
    if (activeKey.value === '0') {
        enterpriseDetai.value.getOrgData()
    }
})
</script>

<style scoped lang="less">
.role-box {
    padding-top: 88px;
    .my-text-base {
        font-size: 16px;
        line-height: 24px;
    }

    .my-m-t-2 {
        margin-top: 8px;
    }

    .my-m-b-4 {
        margin-bottom: 16px;
    }

    .my-m-l-2 {
        margin-left: 8px;
    }

    .go-box {
        font-family: AlibabaPuHuiTi_2_65_Medium;
        margin-bottom: 16px;
        .bt-box-go {
            display: inline-block;
            width: 32px;
            height: 32px;
            line-height: 32px;
            background-color: #fff;
            text-align: center;
            border-radius: 4px;

            &:hover {
                background-color: var(--themeColor);
                color: #fff;
            }
        }
    }
    :deep(.more-icon) {
        width: 20px;
        height: 20px;
        // color: rgba(34, 34, 34, 0.6);
    }
    .role-content {
        background: var(--car-pie-border);
        border-radius: 8px;
        padding: 16px;
        min-height: calc(~'100vh - 186px');

        :deep(.my-tab) {
            .ant-tabs-bar {
                border: 0;
                margin-bottom: 0;
            }

            .ant-tabs-tab {
                padding: 0;
                margin-right: 24px;
                font-size: 14px;
                font-weight: normal;
                line-height: 20px;
                padding-bottom: 10px;
                color: var(--text-100);
                &:hover {
                    color: var(--themeColor);
                }
            }

            .ant-tabs-tab-active {
                color: var(--themeColor);
                font-weight: 500;
                text-shadow: none;
            }

            .ant-tabs-ink-bar {
                // width: 32px !important;
                height: 4px;
                // margin-left: 50%;
                background-color: var(--themeColor);
            }

            .ant-tabs-extra-content {
                // line-height: 46.001px;
                // margin-top: -8px !important;
            }
        }
    }

    .bt {
        padding: 4px 10px;
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        height: 32px;
        line-height: 1;

        &:hover {
            color: var(--themeColor);
            border-color: var(--themeColor);
        }

        :deep(.bt-add) {
            width: 20px;
            height: 20px;
        }

        :deep(.addUser) {
            width: 20px;
            height: 20px;
        }

        .bt-icon-box {
            display: flex;
            align-items: center;
        }
    }

    :deep(.ant-btn-primary) {
        background-color: var(--themeColor);
        border-color: var(--themeColor);

        &:hover {
            color: #fff;
        }
    }

    .margin-l {
        margin-right: 12px;
        background: #f5f7f7;
        border-color: #f5f7f7;
        color: #222222;

        &:hover {
            border-color: #f5f7f7;
            color: #222222;
        }
    }

    .demo-bt {
        background-color: var(--themeColor);
        border-color: var(--themeColor);
    }
}
</style>
