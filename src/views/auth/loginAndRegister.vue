<template>
    <div
        class="video-box"
        :style="{ 'background-image': 'url(' + bgImg.loginBanner + ')' }"
    >
        <div class="w-full h-full flex items-center justify-center content-box">
            <div class="flex-1 p-8" style="height: 540px">
                <div>
                    <img
                        class="logo"
                        :src="loginImg?.orgLogo"
                        alt=""
                        @click="goHome"
                    />
                </div>
                <div class="name-1" :class="locale">
                    {{
                        $t(
                            'AI-Driven Integrated Platform for Generation, Grid, Load, and Storage'
                        )
                    }}
                </div>
                <div class="text-1">
                    {{
                        $t(
                            'Intelligent scheduling optimizes energy efficiency and ensures safe, reliable system operation'
                        )
                    }}
                </div>
            </div>
            <div class="flex flex-col justify-between items-center box p-8">
                <!-- w-full login-box flex justify-end -->
                <div class="loginIn">
                    <div>
                        <div class="bg-ff dark:bg-ff-dark login-content">
                            <!-- <div class="login-title">登录后台</div> -->
                            <router-view></router-view>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { reactive, toRefs, computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
export default {
    setup() {
        // const state = reactive({})
        // return { ...toRefs(state) }
        const hostname = window.location.hostname
        const { t, locale } = useI18n()
        const state = useStore()
        const bgImg = computed(() => {
            if (
                state.getters['user/getConfigData'] &&
                state.getters['user/getConfigData'].loginBanner
            ) {
                //
                console.log(hostname)
                if (hostname == 'wanjiale-ems.ssnj.com') {
                    return state.getters['user/getConfigData']
                } else {
                    return {
                        loginBanner: require('@/assets/login/logobg_simple.jpg'),
                    }
                }
            } else {
                return {
                    loginBanner: require('@/assets/login/logobg_simple.jpg'),
                }
            }
        })

        const loginImg = computed(() => {
            return state.getters['user/getConfigData']
        })
        return {
            bgImg,
            loginImg,
            locale,
        }
    },
}
</script>
<style lang="less" scoped>
.video-box {
    position: relative;
    height: 100vh;
    /*进行视频裁剪*/
    overflow: hidden;
    min-width: 1400px;
    min-height: 760px;
    // background-image: url('../../assets/login/login-wanjiale.jpg');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    z-index: 2;
}
.content-box {
    width: 80%;
    margin: 0 auto;
    .logo {
        width: 234px;
        margin-bottom: 12px;
    }
    .name-1 {
        font-size: 64px;
        color: var(--text-100);
        line-height: 100px;
        text-align: left;
        font-style: normal;
        &.en {
            font-size: 54px;
            line-height: 68px;
            margin-bottom: 16px;
        }
    }
    .text-1 {
        font-size: 28px;
        color: var(--text-100);
        line-height: 40px;
        text-align: left;
        font-style: normal;
    }
}
.loginIn {
    // top: 17%;
    // right: 12%;
    background: #ffffff;
    box-shadow: 0px 0px 20px 0px rgba(131, 176, 193, 0.4);
    z-index: 10;
    .login-content {
        width: 480px;
        height: 540px;
        padding: 38px 42px;
    }
}

.login-title {
    height: 22px;
    font-size: 16px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: rgba(34, 34, 34, 0.65);
    line-height: 22px;
}

.video-box .video-background {
    position: absolute;
    left: 50%;
    top: 50%;
    /*保证视频内容始终居中*/
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    /*保证视频充满屏幕*/
    object-fit: cover;
    min-height: 800px;
}

// .box {
//   background: url("../../assets/login/ssnj.jpg") no-repeat center / cover;
// }
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 88px;
    line-height: 88px;

    // width: 100%;
    .menu {
        // width: 1200px;
        position: relative;
        height: 100%;
    }
}

.login-box {
    width: 1440px;
    // height: calc(~"100vh - 160px");
    // height: 540px;
    margin: auto;
    // overflow: hidden;
    border-radius: 8px;
}
</style>
